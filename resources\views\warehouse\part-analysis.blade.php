@extends('warehouse.content')
@section('resource')
<!-- SweetAlert2 CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css">
<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>

<style>
    .w-fit-content {
        width: fit-content;
    }
    .shadow-kit {
        border: 1px;
        border-radius: 0.5rem;
        background-color: #fff;
    }
    
    .status-ready {
        background-color: #97f784;
        color: #343a40;
    }
    
    .status-not-ready {
        background-color: #eb3124;
        color: white;
    }
    
    .classification-high-demand {
        background-color: #eb3124;
        color: white;
    }
    
    .classification-stable {
        background-color: #97f784;
        color: #343a40;
    }
    
    .classification-low-demand {
        background-color: #feff8c;
        color: #343a40;
    }
    
    .classification-seasonal {
        background-color: #58c0f6;
        color: white;
    }
    
    .classification-overstock {
        background-color: #510968;
        color: white;
    }

    .classification-uncategorized {
        background-color: #6c757d;
        color: white;
    }

    .classification-dormant {
        background-color: #f8f9fa;
        color: #343a40;
        border: 1px solid #dee2e6;
    }
    
    .table-responsive {
        max-height: 70vh;
        overflow-y: auto;
    }
    
    .sticky-header {
        position: sticky;
        top: 0;
        z-index: 10;
        background-color: #f8f9fa;
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh data when filters change
    const siteSelect = document.getElementById('site-select');
    const startDateInput = document.getElementById('start-date');
    const endDateInput = document.getElementById('end-date');
    const statusFilter = document.getElementById('status-filter');
    const classificationFilter = document.getElementById('classification-filter');

    function refreshData() {
        const siteId = siteSelect.value;
        const startDate = startDateInput.value;
        const endDate = endDateInput.value;

        if (!siteId || !startDate || !endDate) {
            return;
        }

        // Show loading
        const tableBody = document.getElementById('analysis-table-body');
        const summaryInfo = document.getElementById('summary-info');

        tableBody.innerHTML = '<tr><td colspan="8" class="text-center"><i class="mdi mdi-loading mdi-spin"></i> Memuat data...</td></tr>';
        summaryInfo.innerHTML = '<i class="mdi mdi-loading mdi-spin"></i> Memuat...';

        // Fetch data
        fetch(`{{ route('warehouse.part-analysis') }}?site_id=${siteId}&start_date=${startDate}&end_date=${endDate}`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            window.currentAnalysisData = data; // Store data globally for filtering
            updateTable(data);
            updateSummary(data);
        })
        .catch(error => {
            console.error('Error:', error);
            tableBody.innerHTML = '<tr><td colspan="8" class="text-center text-danger">Terjadi kesalahan saat memuat data</td></tr>';
            summaryInfo.innerHTML = 'Error';
        });
    }
    
    function updateTable(response) {
        const tableBody = document.getElementById('analysis-table-body');

        if (!response.data || response.data.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="8" class="text-center text-muted">Tidak ada data untuk ditampilkan</td></tr>';
            return;
        }

        // Apply filters
        let filteredData = response.data;

        const statusFilterValue = statusFilter.value;
        const classificationFilterValue = classificationFilter.value;

        if (statusFilterValue) {
            filteredData = filteredData.filter(item => item.status === statusFilterValue);
        }

        if (classificationFilterValue) {
            filteredData = filteredData.filter(item => item.analysis_description === classificationFilterValue);
        }

        if (filteredData.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="8" class="text-center text-muted">Tidak ada data yang sesuai dengan filter</td></tr>';
            return;
        }

        let html = '';
        filteredData.forEach((item, index) => {
            const statusClass = item.status === 'Ready' ? 'status-ready' : 'status-not-ready';
            const classificationClass = getClassificationClass(item.analysis_description);

            html += `
                <tr>
                    <td>${index + 1}</td>
                    <td>${item.part_name}</td>
                    <td>${item.part_code}</td>
                    <td class="text-end">${item.current_stock.toLocaleString('id-ID')}</td>
                    <td class="text-end">${item.in_stock.toLocaleString('id-ID')}</td>
                    <td class="text-end">${item.out_stock.toLocaleString('id-ID')}</td>
                    <td><span class="badge ${statusClass}">${item.status}</span></td>
                    <td><span class="badge ${classificationClass}">${item.analysis_description}</span></td>
                </tr>
            `;
        });

        tableBody.innerHTML = html;

        // Update summary with filtered count
        updateSummary(response, filteredData.length);
    }
    
    function updateSummary(response, filteredCount = null) {
        const summaryInfo = document.getElementById('summary-info');
        const displayCount = filteredCount !== null ? filteredCount : response.total_parts;
        const filterText = filteredCount !== null && filteredCount !== response.total_parts ? ` (${filteredCount} dari ${response.total_parts} ditampilkan)` : '';

        summaryInfo.innerHTML = `
            Total Parts: ${displayCount}${filterText} |
            Periode: ${response.period_start} - ${response.period_end} |
            Tips ; ketepatan Akurasi tergantung jumlah data in dan out stock
        `;
    }

    function getClassificationClass(classification) {
        const classMap = {
            'High Demand': 'classification-high-demand',
            'Stable': 'classification-stable',
            'Low Demand': 'classification-low-demand',
            'Seasonal': 'classification-seasonal',
            'Overstock': 'classification-overstock',
            'Uncategorized': 'classification-uncategorized',
            'Dormant': 'classification-dormant'
        };

        return classMap[classification] || 'classification-uncategorized';
    }

    // Add event listeners
    siteSelect.addEventListener('change', refreshData);
    startDateInput.addEventListener('change', refreshData);
    endDateInput.addEventListener('change', refreshData);
    statusFilter.addEventListener('change', function() {
        // Re-apply filters to current data without fetching new data
        if (window.currentAnalysisData) {
            updateTable(window.currentAnalysisData);
        }
    });
    classificationFilter.addEventListener('change', function() {
        // Re-apply filters to current data without fetching new data
        if (window.currentAnalysisData) {
            updateTable(window.currentAnalysisData);
        }
    });
});
</script>
@endsection

@section('contentho')
<div class="content">
    <div class="container-fluid mt-3">
        <div class="row">
            <div class="col-md-12">
                <div class="card shadow-kit">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center pt-2 pb-1 pl-2 pr-2">
                        <h5 class="mb-0 font-bold text-uppercase text-white">Part Analysis</h5>
                        <div class="d-flex align-items-center">
                            <div id="summary-info" class="text-white mr-3" style="font-size: 11px;">
                                @if(isset($analysisData) && !empty($analysisData['data']))
                                    Total Parts: {{ $analysisData['total_parts'] }} |
                                    Periode: {{ $analysisData['period_start'] }} - {{ $analysisData['period_end'] }} |
                                    Tips ; ketepatan Akurasi tergantung jumlah data in dan out stock
                                @else
                                    Pilih site dan tanggal untuk melihat analisis
                                @endif
                            </div>
                            <button type="button" class="btn btn-outline-light btn-sm" data-toggle="modal" data-target="#helpModal" title="Bantuan">
                                <i class="mdi mdi-help-circle"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Filter Section -->
                    <div class="card-body border-bottom">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label for="site-select" class="form-label">Site</label>
                                <select class="form-select" id="site-select" name="site_id">
                                    <option value="">Pilih Site</option>
                                    @foreach($sites as $site)
                                        <option value="{{ $site->site_id }}" {{ $selectedSite == $site->site_id ? 'selected' : '' }}>
                                            {{ $site->site_name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="start-date" class="form-label">Tanggal Mulai</label>
                                <input type="date" class="form-control" id="start-date" name="start_date" value="{{ $startDate }}">
                            </div>
                            <div class="col-md-2">
                                <label for="end-date" class="form-label">Tanggal Akhir</label>
                                <input type="date" class="form-control" id="end-date" name="end_date" value="{{ $endDate }}">
                            </div>
                            <div class="col-md-2">
                                <label for="status-filter" class="form-label">Status</label>
                                <select class="form-select" id="status-filter">
                                    <option value="">Semua Status</option>
                                    <option value="Ready">Ready</option>
                                    <option value="Not Ready">Not Ready</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="classification-filter" class="form-label">Klasifikasi</label>
                                <select class="form-select" id="classification-filter">
                                    <option value="">Semua Klasifikasi</option>
                                    <option value="High Demand">High Demand</option>
                                    <option value="Stable">Stable</option>
                                    <option value="Low Demand">Low Demand</option>
                                    <option value="Seasonal">Seasonal</option>
                                    <option value="Overstock">Overstock</option>
                                    <option value="Dormant">Dormant</option>
                                    <option value="Uncategorized">Uncategorized</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Table Section -->
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover w-100">
                                <thead class="sticky-header p-2" style="font-size: 12px;">
                                    <tr>
                                        <th class="p-2">No</th>
                                        <th class="p-2">Part Name</th>
                                        <th class="p-2">Part Code</th>
                                        <th class="p-2">Current Stock</th>
                                        <th class="p-2">In Stock</th>
                                        <th class="p-2">Out Stock</th>
                                        <th class="p-2">Status</th>
                                        <th class="p-2">Analysis Description</th>
                                    </tr>
                                </thead>
                                <tbody id="analysis-table-body" style="font-size: 11px;">
                                    @if(isset($analysisData) && !empty($analysisData['data']))
                                        @foreach($analysisData['data'] as $index => $item)
                                        <tr>
                                            <td>{{ $index + 1 }}</td>
                                            <td>{{ $item['part_name'] }}</td>
                                            <td>{{ $item['part_code'] }}</td>
                                            <td class="text-end">{{ number_format($item['current_stock'], 0, ',', '.') }}</td>
                                            <td class="text-end">{{ number_format($item['in_stock'], 0, ',', '.') }}</td>
                                            <td class="text-end">{{ number_format($item['out_stock'], 0, ',', '.') }}</td>
                                            <td>
                                                <span class="badge {{ $item['status'] === 'Ready' ? 'status-ready' : 'status-not-ready' }}">
                                                    {{ $item['status'] }}
                                                </span>
                                            </td>
                                            <td>
                                                @php
                                                    $classificationClass = '';
                                                    switch($item['analysis_description']) {
                                                        case 'High Demand':
                                                            $classificationClass = 'classification-high-demand';
                                                            break;
                                                        case 'Stable':
                                                            $classificationClass = 'classification-stable';
                                                            break;
                                                        case 'Low Demand':
                                                            $classificationClass = 'classification-low-demand';
                                                            break;
                                                        case 'Seasonal':
                                                            $classificationClass = 'classification-seasonal';
                                                            break;
                                                        case 'Overstock':
                                                            $classificationClass = 'classification-overstock';
                                                            break;
                                                        case 'Dormant':
                                                            $classificationClass = 'classification-dormant';
                                                            break;
                                                        default:
                                                            $classificationClass = 'classification-uncategorized';
                                                    }
                                                @endphp
                                                <span class="badge {{ $classificationClass }}">
                                                    {{ $item['analysis_description'] }}
                                                </span>
                                            </td>
                                        </tr>
                                        @endforeach
                                    @else
                                        <tr>
                                            <td colspan="8" class="text-center text-muted">Pilih site dan tanggal untuk melihat analisis part</td>
                                        </tr>
                                    @endif
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Help Modal -->
<div class="modal fade" id="helpModal" tabindex="-1" aria-labelledby="helpModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="helpModalLabel">
                    <i class="mdi mdi-help-circle mr-2"></i>Panduan Part Analysis
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12">
                        <h6 class="text-primary mb-3"><i class="mdi mdi-information-outline mr-2"></i>Status Part</h6>
                        <div class="mb-4">
                            <div class="d-flex align-items-center mb-2">
                                <span class="badge status-ready mr-2">Ready</span>
                                <span>Part tersedia di gudang (stok > 0)</span>
                            </div>
                            <div class="d-flex align-items-center">
                                <span class="badge status-not-ready mr-2">Not Ready</span>
                                <span>Part tidak tersedia di gudang (stok = 0)</span>
                            </div>
                        </div>

                        <h6 class="text-primary mb-3"><i class="mdi mdi-chart-line mr-2"></i>Klasifikasi Analisis</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="d-flex align-items-center mb-1">
                                        <span class="badge classification-high-demand mr-2">High Demand</span>
                                        <strong>Permintaan Tinggi</strong>
                                    </div>
                                    <small class="text-muted">Part dengan penggunaan konsisten dan tinggi. Perlu monitoring ketat untuk menghindari kehabisan stok.</small>
                                </div>

                                <div class="mb-3">
                                    <div class="d-flex align-items-center mb-1">
                                        <span class="badge classification-stable mr-2">Stable</span>
                                        <strong>Stabil</strong>
                                    </div>
                                    <small class="text-muted">Part dengan pola masuk dan keluar yang seimbang. Manajemen stok normal.</small>
                                </div>

                                <div class="mb-3">
                                    <div class="d-flex align-items-center mb-1">
                                        <span class="badge classification-low-demand mr-2">Low Demand</span>
                                        <strong>Permintaan Rendah</strong>
                                    </div>
                                    <small class="text-muted">Part dengan penggunaan minimal tapi teratur. Stok dapat dikurangi.</small>
                                </div>

                                <div class="mb-3">
                                    <div class="d-flex align-items-center mb-1">
                                        <span class="badge classification-seasonal mr-2">Seasonal</span>
                                        <strong>Musiman</strong>
                                    </div>
                                    <small class="text-muted">Part dengan pola penggunaan tidak teratur, hanya digunakan pada waktu tertentu.</small>
                                </div>

                                <div class="mb-3">
                                    <div class="d-flex align-items-center mb-1">
                                        <span class="badge classification-overstock mr-2">Overstock</span>
                                        <strong>Kelebihan Stok</strong>
                                    </div>
                                    <small class="text-muted">Stok jauh melebihi pola penggunaan. Pertimbangkan untuk mengurangi stok.</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="d-flex align-items-center mb-1">
                                        <span class="badge classification-dormant mr-2">Dormant</span>
                                        <strong>Tidak Aktif</strong>
                                    </div>
                                    <small class="text-muted">Part memiliki stok tapi tidak ada aktivitas transaksi dalam periode analisis.</small>
                                </div>

                                <div class="mb-3">
                                    <div class="d-flex align-items-center mb-1">
                                        <span class="badge classification-uncategorized mr-2">Uncategorized</span>
                                        <strong>Tidak Terkategori</strong>
                                    </div>
                                    <small class="text-muted">Part tanpa stok dan tanpa aktivitas transaksi sama sekali.</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>
@endsection
