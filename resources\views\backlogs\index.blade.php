@extends('sites.content')
@section('title', 'Backlog Management')
@section('resourcesite')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<style>
.backlog-form {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}
.unit-autocomplete {
    position: relative;
}
.unit-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 5px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}
.unit-dropdown-item {
    padding: 10px;
    cursor: pointer;
    border-bottom: 1px solid #eee;
}
.unit-dropdown-item:hover {
    background: #f8f9fa;
}
.skeleton-loader {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}
@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}
.skeleton-row {
    height: 50px;
    margin-bottom: 10px;
    border-radius: 5px;
}
.modal-backdrop {
    z-index: 1040;
}
.modal {
    z-index: 1050;
}

/* Parts autocomplete styles */
.part-autocomplete {
    position: relative;
}

.part-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-top: none;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.part-dropdown-item {
    padding: 10px;
    cursor: pointer;
    border-bottom: 1px solid #eee;
}

.part-dropdown-item:last-child {
    border-bottom: none;
}

.part-dropdown-item:hover {
    background-color: #f8f9fa;
}

#parts-table {
    min-height: 100px;
}

#parts-empty-message {
    display: block;
}

.has-parts #parts-empty-message {
    display: none;
}
</style>
@endsection

@section('contentsite')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('sites.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Backlog Management</li>
                    </ol>
                </div>
                <h4 class="page-title">Backlog Management</h4>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Full Width Table -->
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h4 class="header-title">Daftar Backlog</h4>
                        </div>
                        <div class="col-md-6 text-end">
                            <button type="button" class="btn btn-primary" id="btn-add-backlog">
                                <i class="mdi mdi-plus"></i> Tambah Backlog
                            </button>
                        </div>
                    </div>

                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <select class="btn btn-sm btn-primary form-select-sm" id="filter-status">
                                <option value="">Semua Status</option>
                                <option value="OPEN">Open</option>
                                <option value="CLOSED">Closed</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <input type="text" class="form-control form-control-sm" id="filter-unit" placeholder="Unit Code">
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control form-control-sm" id="filter-start-date">
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control form-control-sm" id="filter-end-date">
                        </div>
                    </div>

                    <!-- Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Tanggal</th>
                                    <th>Unit Code</th>
                                    <th>HM Found</th>
                                    <th>Problem</th>
                                    <th>Backlog Job</th>
                                    <th>Plan HM</th>
                                    <th>Status</th>
                                    <th>Plan Pull Date</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody id="backlog-table-body">
                                <!-- Data will be loaded via AJAX -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center" id="pagination-container">
                        <!-- Pagination will be loaded via AJAX -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Unified Modal for Add/Edit Backlog -->
<div class="modal fade" id="backlogModal" tabindex="-1" aria-labelledby="backlogModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="backlogModalLabel">Tambah Backlog</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="backlog-form">
                    <input type="hidden" id="backlog-id">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="unit_code" class="form-label">Unit Code <span class="text-danger">*</span></label>
                                <div class="unit-autocomplete">
                                    <input type="text" class="form-control" id="unit_code" name="unit_code" placeholder="Ketik untuk mencari unit..." autocomplete="off" required>
                                    <div class="unit-dropdown" id="unit-dropdown"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="hm_found" class="form-label">HM Found</label>
                                <input type="number" class="form-control" id="hm_found" name="hm_found" step="0.01" min="0">
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="problem_description" class="form-label">Problem Description <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="problem_description" name="problem_description" required>
                    </div>

                    <div class="mb-3">
                        <label for="backlog_job" class="form-label">Backlog Job <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="backlog_job" name="backlog_job" required>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="plan_hm" class="form-label">Plan HM</label>
                                <input type="number" class="form-control" id="plan_hm" name="plan_hm" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                <select class="form-select" id="status" name="status" required>
                                    <option value="OPEN">Open</option>
                                    <option value="CLOSED">Closed</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="plan_pull_date" class="form-label">Plan Pull Date</label>
                        <input type="datetime-local" class="form-control" id="plan_pull_date" name="plan_pull_date">
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>

                    <!-- Parts Management Section -->
                    <div class="mb-3">
                        <label class="form-label">Parts <span class="text-danger">*</span></label>
                        <div class="card">
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="part-autocomplete">
                                            <input type="text" class="form-control" id="part_search" placeholder="Cari part..." autocomplete="off">
                                            <div class="part-dropdown" id="part-dropdown"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <input type="number" class="form-control" id="part_quantity" placeholder="Quantity" min="1" value="1">
                                    </div>
                                    <div class="col-md-3">
                                        <button type="button" class="btn btn-primary" id="btn-add-part">
                                            <i class="mdi mdi-plus"></i> Tambah
                                        </button>
                                    </div>
                                </div>

                                <div class="table-responsive">
                                    <table class="table table-sm table-bordered" id="parts-table">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Part Code</th>
                                                <th>Part Name</th>
                                                <th>Quantity</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody id="parts-table-body">
                                            <!-- Parts will be added here -->
                                        </tbody>
                                    </table>
                                </div>

                                <div id="parts-empty-message" class="text-center text-muted py-3">
                                    <i class="mdi mdi-package-variant"></i><br>
                                    Belum ada part yang ditambahkan.<br>
                                    <small>Setiap backlog harus memiliki minimal 1 part.</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="mdi mdi-close"></i> Batal
                </button>
                <button type="button" class="btn btn-primary" id="btn-submit-backlog">
                    <i class="mdi mdi-content-save"></i> Simpan
                </button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    let isEditing = false;
    let editingId = null;
    let currentPage = 1;

    // Parts management variables
    let selectedParts = [];

    // Load initial data
    loadBacklogData();

    // Show add modal
    $('#btn-add-backlog').click(function() {
        showAddModal();
    });

    // Parts autocomplete
    $('#part_search').on('input', function() {
        const search = $(this).val();
        if (search.length >= 2) {
            searchParts(search);
        } else {
            hidePartDropdown();
        }
    });

    // Add part button
    $('#btn-add-part').click(function() {
        addPartToList();
    });

    // Hide part dropdown when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.part-autocomplete').length) {
            hidePartDropdown();
        }
    });

    // Unit autocomplete
    $('#unit_code').on('input', function() {
        const search = $(this).val();
        if (search.length >= 2) {
            searchUnits(search, 'unit-dropdown');
        } else {
            hideUnitDropdown('unit-dropdown');
        }
    });

    // Hide dropdown when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.unit-autocomplete').length) {
            hideUnitDropdown('unit-dropdown');
        }
    });

    // Form submission
    $('#btn-submit-backlog').click(function() {
        submitForm();
    });

    // Edit button
    $(document).on('click', '.btn-edit', function() {
        const id = $(this).data('id');
        editBacklog(id);
    });

    // Delete button
    $(document).on('click', '.btn-delete', function() {
        const id = $(this).data('id');
        deleteBacklog(id);
    });

    // Filters - use debounce for better performance
    let filterTimeout;
    $('#filter-status, #filter-unit, #filter-start-date, #filter-end-date').on('change input', function() {
        clearTimeout(filterTimeout);
        filterTimeout = setTimeout(function() {
            currentPage = 1;
            loadBacklogData();
        }, 300);
    });

    // Pagination click handler
    $(document).on('click', '.pagination a', function(e) {
        e.preventDefault();
        const url = $(this).attr('href');
        if (url) {
            const page = new URL(url).searchParams.get('page');
            currentPage = page || 1;
            loadBacklogData();
        }
    });

    // Load backlog data via AJAX
    function loadBacklogData() {
        showSkeletonLoader();

        const params = {
            page: currentPage,
            status: $('#filter-status').val(),
            unit_code: $('#filter-unit').val(),
            start_date: $('#filter-start-date').val(),
            end_date: $('#filter-end-date').val()
        };

        $.ajax({
            url: '{{ route("backlogs.data") }}',
            type: 'GET',
            data: params,
            success: function(response) {
                renderBacklogTable(response.data);
                renderPagination(response);
            },
            error: function() {
                $('#backlog-table-body').html('<tr><td colspan="9" class="text-center text-danger">Terjadi kesalahan saat memuat data</td></tr>');
                $('#pagination-container').html('');
            }
        });
    }

    function showSkeletonLoader() {
        let skeletonHtml = '';
        for (let i = 0; i < 5; i++) {
            skeletonHtml += `
                <tr>
                    <td><div class="skeleton-loader skeleton-row"></div></td>
                    <td><div class="skeleton-loader skeleton-row"></div></td>
                    <td><div class="skeleton-loader skeleton-row"></div></td>
                    <td><div class="skeleton-loader skeleton-row"></div></td>
                    <td><div class="skeleton-loader skeleton-row"></div></td>
                    <td><div class="skeleton-loader skeleton-row"></div></td>
                    <td><div class="skeleton-loader skeleton-row"></div></td>
                    <td><div class="skeleton-loader skeleton-row"></div></td>
                    <td><div class="skeleton-loader skeleton-row"></div></td>
                </tr>
            `;
        }
        $('#backlog-table-body').html(skeletonHtml);
    }

    function renderBacklogTable(backlogs) {
        let html = '';

        if (backlogs.length === 0) {
            html = '<tr><td colspan="9" class="text-center text-muted">Tidak ada data backlog</td></tr>';
        } else {
            backlogs.forEach(function(backlog) {
                html += `
                    <tr>
                        <td>${backlog.formatted_created_date}</td>
                        <td>
                            <strong>${backlog.unit_code}</strong><br>
                            <small class="text-muted">${backlog.unit_type || '-'}</small>
                        </td>
                        <td>${backlog.hm_found || '-'}</td>
                        <td>${backlog.problem_description}</td>
                        <td>${backlog.backlog_job}</td>
                        <td>${backlog.plan_hm || '-'}</td>
                        <td>
                            <span class="badge ${backlog.status_badge_class}">${backlog.status}</span>
                        </td>
                        <td>${backlog.formatted_plan_pull_date || '-'}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-primary btn-edit" data-id="${backlog.id}">
                                    <i class="mdi mdi-pencil"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger btn-delete" data-id="${backlog.id}">
                                    <i class="mdi mdi-delete"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            });
        }

        $('#backlog-table-body').html(html);
    }

    function renderPagination(data) {
        const container = $('#pagination-container');
        container.html('');

        if (data.last_page <= 1) return;

        const pagination = $('<ul class="pagination pagination-rounded justify-content-center mb-0"></ul>');

        // Previous button
        if (data.current_page > 1) {
            pagination.append(createPaginationItem(data.current_page - 1, '«'));
        }

        // Page numbers
        for (let i = 1; i <= data.last_page; i++) {
            pagination.append(createPaginationItem(i, i, i === data.current_page));
        }

        // Next button
        if (data.current_page < data.last_page) {
            pagination.append(createPaginationItem(data.current_page + 1, '»'));
        }

        container.append(pagination);
    }

    function createPaginationItem(page, text, isActive = false) {
        const li = $(`<li class="page-item ${isActive ? 'active' : ''}"></li>`);
        const a = $(`<a class="page-link" href="#" data-page="${page}">${text}</a>`);

        a.on('click', function(e) {
            e.preventDefault();
            if (!isActive) {
                currentPage = page;
                loadBacklogData();
            }
        });

        li.append(a);
        return li;
    }

    function showAddModal() {
        isEditing = false;
        editingId = null;
        $('#backlogModalLabel').text('Tambah Backlog');
        $('#btn-submit-backlog').html('<i class="mdi mdi-content-save"></i> Simpan');
        $('#backlog-form')[0].reset();
        $('#backlog-id').val('');
        selectedParts = [];
        renderPartsTable();
        $('#backlogModal').modal('show');
    }

    function showEditModal(backlog) {
        isEditing = true;
        editingId = backlog.id;
        $('#backlogModalLabel').text('Edit Backlog');
        $('#btn-submit-backlog').html('<i class="mdi mdi-content-save"></i> Update');

        // Fill form with backlog data
        $('#backlog-id').val(backlog.id);
        $('#unit_code').val(backlog.unit_code);
        $('#hm_found').val(backlog.hm_found);
        $('#problem_description').val(backlog.problem_description);
        $('#backlog_job').val(backlog.backlog_job);
        $('#plan_hm').val(backlog.plan_hm);
        $('#status').val(backlog.status);
        $('#notes').val(backlog.notes);

        // Format datetime for input
        if (backlog.plan_pull_date) {
            const date = new Date(backlog.plan_pull_date);
            const formattedDate = date.toISOString().slice(0, 16);
            $('#plan_pull_date').val(formattedDate);
        }

        // Load parts data
        selectedParts = backlog.parts || [];
        renderPartsTable();

        $('#backlogModal').modal('show');
    }

    function searchUnits(search, dropdownId) {
        $.ajax({
            url: '{{ route("backlogs.search-units") }}',
            type: 'GET',
            data: { search: search },
            success: function(response) {
                renderUnitDropdown(response.data, dropdownId);
            },
            error: function() {
                hideUnitDropdown(dropdownId);
            }
        });
    }

    function renderUnitDropdown(units, dropdownId) {
        let html = '';

        if (units.length === 0) {
            html = '<div class="unit-dropdown-item text-muted">Unit tidak ditemukan</div>';
        } else {
            units.forEach(function(unit) {
                html += `
                    <div class="unit-dropdown-item" onclick="selectUnit('${unit.unit_code}', '${unit.unit_type}', 'unit_code', '${dropdownId}')">
                        <strong>${unit.unit_code}</strong><br>
                        <small class="text-muted">${unit.unit_type}</small>
                    </div>
                `;
            });
        }

        $('#' + dropdownId).html(html).show();
    }

    function hideUnitDropdown(dropdownId) {
        $('#' + dropdownId).hide();
    }

    window.selectUnit = function(unitCode, unitType, inputId, dropdownId) {
        $('#' + inputId).val(unitCode);
        hideUnitDropdown(dropdownId);
    };

    function submitForm() {
        // Validate parts
        if (selectedParts.length === 0) {
            Swal.fire({
                icon: 'error',
                title: 'Validasi Error',
                text: 'Setiap backlog harus memiliki minimal 1 part.'
            });
            return;
        }

        const formData = new FormData($('#backlog-form')[0]);
        const url = isEditing ? `/backlogs/${editingId}` : '{{ route("backlogs.store") }}';

        if (isEditing) {
            formData.append('_method', 'PUT');
        }

        // Add parts data
        selectedParts.forEach((part, index) => {
            formData.append(`parts[${index}][part_code]`, part.part_code);
            formData.append(`parts[${index}][quantity]`, part.quantity);
        });

        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Berhasil',
                        text: response.message,
                        timer: 1500,
                        showConfirmButton: false
                    }).then(() => {
                        $('#backlogModal').modal('hide');
                        loadBacklogData();
                    });
                }
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    const errors = xhr.responseJSON.errors;
                    let errorMessage = '';
                    Object.keys(errors).forEach(key => {
                        errorMessage += errors[key][0] + '\n';
                    });

                    Swal.fire({
                        icon: 'error',
                        title: 'Validasi Error',
                        text: errorMessage
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Terjadi kesalahan saat menyimpan data'
                    });
                }
            }
        });
    }

    function editBacklog(id) {
        $.ajax({
            url: `/backlogs/${id}`,
            type: 'GET',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    showEditModal(response.data);
                }
            },
            error: function() {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'Terjadi kesalahan saat mengambil data backlog'
                });
            }
        });
    }

    function deleteBacklog(id) {
        Swal.fire({
            title: 'Konfirmasi Hapus',
            text: 'Apakah Anda yakin ingin menghapus backlog ini?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Ya, Hapus!',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: `/backlogs/${id}`,
                    type: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire({
                                icon: 'success',
                                title: 'Berhasil',
                                text: response.message,
                                timer: 1500,
                                showConfirmButton: false
                            }).then(() => {
                                loadBacklogData();
                            });
                        }
                    },
                    error: function() {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'Terjadi kesalahan saat menghapus data'
                        });
                    }
                });
            }
        });
    }

    // Parts management functions
    function searchParts(search) {
        $.ajax({
            url: '{{ route("backlogs.search-parts") }}',
            type: 'GET',
            data: { search: search },
            success: function(response) {
                renderPartDropdown(response.data);
            },
            error: function() {
                hidePartDropdown();
            }
        });
    }

    function renderPartDropdown(parts) {
        let html = '';

        if (parts.length === 0) {
            html = '<div class="part-dropdown-item text-muted">Part tidak ditemukan</div>';
        } else {
            parts.forEach(function(part) {
                html += `
                    <div class="part-dropdown-item" onclick="selectPart('${part.part_code}', '${part.part_name}')">
                        <strong>${part.part_code}</strong><br>
                        <small class="text-muted">${part.part_name}</small>
                    </div>
                `;
            });
        }

        $('#part-dropdown').html(html).show();
    }

    function hidePartDropdown() {
        $('#part-dropdown').hide();
    }

    window.selectPart = function(partCode, partName) {
        $('#part_search').val(partCode + ' - ' + partName);
        $('#part_search').data('part-code', partCode);
        $('#part_search').data('part-name', partName);
        hidePartDropdown();
    };

    function addPartToList() {
        const partCode = $('#part_search').data('part-code');
        const partName = $('#part_search').data('part-name');
        const quantity = parseInt($('#part_quantity').val()) || 1;

        if (!partCode) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Silakan pilih part terlebih dahulu'
            });
            return;
        }

        // Check if part already exists
        const existingIndex = selectedParts.findIndex(p => p.part_code === partCode);
        if (existingIndex !== -1) {
            selectedParts[existingIndex].quantity = quantity;
        } else {
            selectedParts.push({
                part_code: partCode,
                part_name: partName,
                quantity: quantity
            });
        }

        // Clear inputs
        $('#part_search').val('').removeData('part-code').removeData('part-name');
        $('#part_quantity').val(1);

        renderPartsTable();
    }

    function renderPartsTable() {
        const tbody = $('#parts-table-body');
        tbody.empty();

        if (selectedParts.length === 0) {
            $('#parts-empty-message').show();
            $('.card').removeClass('has-parts');
        } else {
            $('#parts-empty-message').hide();
            $('.card').addClass('has-parts');

            selectedParts.forEach(function(part, index) {
                const row = `
                    <tr>
                        <td>${part.part_code}</td>
                        <td>${part.part_name}</td>
                        <td>${part.quantity}</td>
                        <td>
                            <button type="button" class="btn btn-sm btn-danger" onclick="removePart(${index})">
                                <i class="mdi mdi-delete"></i>
                            </button>
                        </td>
                    </tr>
                `;
                tbody.append(row);
            });
        }
    }

    window.removePart = function(index) {
        selectedParts.splice(index, 1);
        renderPartsTable();
    };
});
</script>
@endsection
