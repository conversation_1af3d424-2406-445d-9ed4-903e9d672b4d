
<?php $__env->startSection('contentho'); ?>
<?php $__env->startSection('title', 'Dashboard HO'); ?>
<!-- mulai content disin -->
<div class="row page-title-box mb-2">
    <div class="col d-flex align-items-center" style="max-width: fit-content;">
        <button class="button-menu-mobile text-blue-500 btn-sm">
            <i style="font-size: 30px;" class="mdi mdi-menu"></i>
        </button>
    </div>

    <div class="col">
        <form method="GET" action="<?php echo e(route('adminho.dashboard')); ?>" class="py-2">
            <div class="d-flex align-items-center gap-3 flex-wrap">
                <div class="btn-group" role="group">
                    <div class="form-check form-check-inline">
                        <input type="radio" class="form-check-input" name="group_by" id="day" value="day"
                            <?php echo e($groupBy === 'day' ? 'checked' : ''); ?>>
                        <label class="form-check-label" for="day">Day</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input type="radio" class="form-check-input" name="group_by" id="week" value="week"
                            <?php echo e($groupBy === 'week' ? 'checked' : ''); ?>>
                        <label class="form-check-label" for="week">Week</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input type="radio" class="form-check-input" name="group_by" id="month" value="month"
                            <?php echo e($groupBy === 'month' ? 'checked' : ''); ?>>
                        <label class="form-check-label" for="month">Month</label>
                    </div>
                </div>

                <div class="d-flex align-items-center gap-3">
                    <input type="date" class="form-control form-control-sm" name="start_date" value="<?php echo e($startDate); ?>">
                    <input type="date" class="form-control form-control-sm" name="end_date" value="<?php echo e($endDate); ?>">
                    <button type="submit" class="btn btn-primary btn-sm">Cari</button>
                </div>
            </div>
        </form>
    </div>
    <div class="col">
        <ul class="list-unstyled topnav-menu float-right mb-0">
            <li class="dropdown notification-list">
                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="mdi mdi-bell-outline noti-icon"></i>
                    <span class="badge badge-danger badge-pill float-right badgenotif"></span>
                </a>
                <div class="dropdown-menu dropdown-menu-end dropdown-lg">
                    <div class="dropdown-item noti-title bg-primary">
                        <h5 class="m-0 text-white d-flex justify-content-between align-items-center">
                            Notifikasi
                            <a href="javascript:void(0);" class="text-white" id="clear-all">
                                <small></small>
                            </a>
                        </h5>
                    </div>
                    <div class="noti-scroll" id="notification-list">
                        <!-- Notifications will be dynamically inserted here -->
                    </div>
                </div>
            </li>
        </ul>
    </div>
</div>

<!-- <div class="row m-2">
    <div class="col col-md-6 p-0">
        <div class="shadow-kit bgchart rounded-lg">
            <h5 class="h5 text-uppercase p-3">Out Stock Chart</h5>
            <div style="min-height: 400px;" class="chart-container">
                <canvas id="outStockChart"></canvas>
            </div>
        </div>
    </div>
    <div class="col col-md-6">
        <div class="shadow-kit bgchart2 rounded-lg">
            <h5 class="h5 text-uppercase p-3">In Stock Chart</h5>
            <div style="min-height: 400px;" class="chart-container">
                <canvas id="inStockChart"></canvas>
            </div>
        </div>
    </div>
</div> -->

<div class="card">
    <div class="px-4 py-2">
        <div class="h5 text-bold text-blue-900 text-uppercase">Service Part Terdekat</div>
    </div>
    <div class="px-4 py-1">
        <table class="table">
            <thead class="bg-dark text-white">
                <tr>
                    <td>Nama Part</td>
                    <td>SITE</td>
                    <td>Unit Code</td>
                    <td>Jumlah</td>
                    <td>Stock Di Site</td>
                    <td>Tanggal Service</td>
                </tr>
            </thead>
            <tbody>
                <?php $__currentLoopData = $partsneed; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <tr>
                        <td><?php echo e($data->part_name); ?></td>
                        <td><?php echo e($data->site_id); ?></td>
                        <td><?php echo e($data->unit_code); ?></td>
                        <td><?php echo e($data->total_quantityneed); ?></td>
                        <td><?php echo e($data->stock_quantity); ?></td>
                        <td><?php echo e($data->estimated_next_service_date); ?></td>
                    </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </tbody>
        </table>
    </div>
</div>

<div class="row m-2">
    <div class="col">
        <div class="mt-2">
            <div class="row">
                <?php $__currentLoopData = $inventoryData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col mb-2">
                    <div class="shadow-kit rounded-lg bgwhite rounded-lg">
                        <div class="h5 text-uppercase p-2 m-0 text-center">Persentasi <?php echo e($data['site_name']); ?></div>
                        <div class="card-body ">
                            <canvas id="pieChart<?php echo e(Str::slug($data['site_name'])); ?>" width="300" height="300"></canvas>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>
    <div class="col">
        <div class="col">
            <div class="mt-2">
                <?php $__currentLoopData = $inventoryData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php if(count($data['not_ready_parts']) > 0 || count($data['medium_parts']) > 0): ?>
                <div class="pl-2 pr-4 shadow-kit bgwhite p-4 pb-0 mb-1 rounded-lg">
                    <h5 class="text-uppercase text-bold">Status Part di <?php echo e($data['site_name']); ?></h5>
                    <hr>
                    <?php if(count($data['not_ready_parts']) > 0): ?>
                    <div class="table-responsive">
                        <h6>Part Not Ready</h6>
                        <table class="table table-bordered w-100" id="not-ready-table-<?php echo e($loop->index); ?>">
                            <thead class="table-dark text-white">
                                <tr>
                                    <th class="p-2 text-center">No</th>
                                    <th class="p-2">Nama Part</th>
                                    <th class="p-2">Min</th>
                                    <th class="p-2">Max</th>
                                    <th class="p-2">Stock Tersisa</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $i = 0; ?>
                                <?php $__currentLoopData = $data['not_ready_parts']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $part): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr class="<?php echo e($part['status'] == 'danger' ? 'table-danger' : ''); ?>">
                                    <td class="text-center"><?php echo e($i += 1); ?></td>
                                    <td><?php echo e($part['part_name']); ?></td>
                                    <td><?php echo e($part['min_stock']); ?></td>
                                    <td><?php echo e($part['max_stock']); ?></td>
                                    <td><?php echo e($part['stock_quantity']); ?></td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                        <div id="not-ready-pagination-<?php echo e($loop->index); ?>" class="pagination-container mt-3">
                            <!-- Pagination will be rendered here by JavaScript -->
                        </div>
                    </div>
                    <?php endif; ?>
                    <?php if(count($data['medium_parts']) > 0): ?>
                    <div class="table-responsive">
                        <h6>Part Hampir Habis (Stock mendekati Minimum)</h6>
                        <table class="table table-bordered w-100" id="medium-table-<?php echo e($loop->index); ?>">
                            <thead class="table-primary">
                                <tr>
                                    <th class="p-2 text-center">No</th>
                                    <th class="p-2">Nama Part</th>
                                    <th class="p-2">Min</th>
                                    <th class="p-2">Max</th>
                                    <th class="p-2">Stock Tersisa</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $i = 0; ?>
                                <?php $__currentLoopData = $data['medium_parts']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $part): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr class="<?php echo e($part['status'] == 'warning' ? 'table-warning' : ''); ?>">
                                    <td class="text-center"><?php echo e($i += 1); ?></td>
                                    <td><?php echo e($part['part_name']); ?></td>
                                    <td><?php echo e($part['min_stock']); ?></td>
                                    <td><?php echo e($part['max_stock']); ?></td>
                                    <td><?php echo e($part['stock_quantity']); ?></td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                        <div id="medium-pagination-<?php echo e($loop->index); ?>" class="pagination-container mt-3">
                            <!-- Pagination will be rendered here by JavaScript -->
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('resource'); ?>
<!-- Pagination Styling -->
<style>
    .pagination-container {
        display: flex;
        justify-content: center;
    }

    .pagination {
        display: flex;
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .pagination li {
        margin: 0 2px;
    }

    .pagination li a {
        color: #333;
        background-color: #fff;
        border: 1px solid #ddd;
        padding: 6px 12px;
        text-decoration: none;
        border-radius: 4px;
        cursor: pointer;
        display: block;
    }

    .pagination li.active a {
        color: #fff;
        background-color: #28a745;
        border-color: #28a745;
    }

    .pagination li a:hover {
        background-color: #f8f9fa;
    }

    .pagination li.disabled a {
        color: #6c757d;
        pointer-events: none;
        background-color: #fff;
        border-color: #ddd;
    }
</style>

<?php echo app('Illuminate\Foundation\Vite')(['resources/js/chart.js','resources/js/style.js','resources/js/piechart.js']); ?>
<script>
    window.inStockData = <?php echo json_encode($inStockChartData, 15, 512) ?>;
    window.outStockData = <?php echo json_encode($outStockChartData, 15, 512) ?>;
    window.groupBy = <?php echo json_encode($groupBy, 15, 512) ?>;
    window.inventoryData = <?php echo json_encode($inventoryData, 15, 512) ?>;

    // Pagination functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Function to create pagination for a table
        function setupPagination(tableId, paginationId, itemsPerPage = 5) {
            const table = document.getElementById(tableId);
            if (!table) return;

            const tbody = table.querySelector('tbody');
            const rows = tbody.querySelectorAll('tr');
            const totalPages = Math.ceil(rows.length / itemsPerPage);

            let currentPage = 1;

            // Function to show the appropriate rows for the current page
            function showPage(page) {
                const start = (page - 1) * itemsPerPage;
                const end = start + itemsPerPage;

                // Hide all rows
                rows.forEach((row, index) => {
                    row.style.display = (index >= start && index < end) ? '' : 'none';
                });

                // Update pagination UI
                updatePagination();
            }

            // Function to create pagination controls
            function updatePagination() {
                const paginationContainer = document.getElementById(paginationId);
                if (!paginationContainer) return;

                paginationContainer.innerHTML = '';

                if (totalPages <= 1) return; // Don't show pagination if only one page

                const ul = document.createElement('ul');
                ul.className = 'pagination pagination-rounded ';

                // Previous button
                const prevLi = document.createElement('li');
                prevLi.className = currentPage === 1 ? 'disabled' : '';
                const prevA = document.createElement('a');
                prevA.href = '#';
                prevA.textContent = '\u00ab';
                prevA.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (currentPage > 1) {
                        currentPage--;
                        showPage(currentPage);
                    }
                });
                prevLi.appendChild(prevA);
                ul.appendChild(prevLi);

                // Page numbers
                for (let i = 1; i <= totalPages; i++) {
                    const li = document.createElement('li');
                    li.className = i === currentPage ? 'active' : '';
                    const a = document.createElement('a');
                    a.href = '#';
                    a.textContent = i;
                    a.addEventListener('click', function(e) {
                        e.preventDefault();
                        currentPage = i;
                        showPage(currentPage);
                    });
                    li.appendChild(a);
                    ul.appendChild(li);
                }

                // Next button
                const nextLi = document.createElement('li');
                nextLi.className = currentPage === totalPages ? 'disabled' : '';
                const nextA = document.createElement('a');
                nextA.href = '#';
                nextA.textContent = '\u00bb';
                nextA.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (currentPage < totalPages) {
                        currentPage++;
                        showPage(currentPage);
                    }
                });
                nextLi.appendChild(nextA);
                ul.appendChild(nextLi);

                paginationContainer.appendChild(ul);
            }

            // Initialize pagination
            showPage(currentPage);
        }

        // Setup pagination for all tables
        const inventoryData = window.inventoryData || [];
        inventoryData.forEach((data, index) => {
            if (data.not_ready_parts && data.not_ready_parts.length > 0) {
                setupPagination(`not-ready-table-${index}`, `not-ready-pagination-${index}`, 5);
            }
            if (data.medium_parts && data.medium_parts.length > 0) {
                setupPagination(`medium-table-${index}`, `medium-pagination-${index}`, 5);
            }
        });
    });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('warehouse.content', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\portalpwb\resources\views/warehouse/dashboard.blade.php ENDPATH**/ ?>