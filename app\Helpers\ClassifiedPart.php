<?php

namespace App\Helpers;

class ClassifiedPart
{
    /**
     * Classify part demand based on stock-out history and current stock.
     *
     * @param array<int> $stockOutHistory  Monthly/Weekly stock-out quantities, e.g., [2, 3, 1, 0, 5, 6]
     * @param int        $stockNow         Current stock on hand
     *
     * @return string                      One of: 'High Demand', 'Stable', 'Low Demand', 'Seasonal', 'Overstocked', 'Uncategorized', 'Data Insufficient'
     */
    public static function classify(array $stockOutHistory, int $stockNow): string
    {
        // Constants (thresholds can be adjusted here)
        $MIN_REQUIRED_MONTHS = 4;
        $HIGH_DEMAND_AVG = 10;
        $STABLE_AVG_MIN = 1;
        $STABLE_AVG_MAX = 3;
        $LOW_DEMAND_AVG_MAX = 3;
        $OVERSTOCKED_MULTIPLIER = 6;

        $months = count($stockOutHistory);

        if ($months < $MIN_REQUIRED_MONTHS) {
            return 'Data Insufficient';
        }

        $averageUsage = array_sum($stockOutHistory) / $months;

        $variance = array_sum(array_map(
            fn($v) => pow($v - $averageUsage, 2),
            $stockOutHistory
        )) / $months;

        $stdDeviation = sqrt($variance);

        $usedMonths = count(array_filter($stockOutHistory, fn($v) => $v > 0));
        $usedPercent = $usedMonths / $months;

        // Classification Rules
        if (
            $averageUsage >= $HIGH_DEMAND_AVG &&
            $stdDeviation <= 2 &&
            $usedPercent > 0.8
        ) {
            return 'High Demand';
        }

        if (
            $averageUsage >= $STABLE_AVG_MIN &&
            $averageUsage <= $STABLE_AVG_MAX &&
            $usedPercent > 0.8 &&
            $stdDeviation <= 1.5
        ) {
            return 'Stable';
        }

        if (
            $usedPercent >= 0.7 &&
            $averageUsage < $LOW_DEMAND_AVG_MAX &&
            $stockNow < ($averageUsage * 2)
        ) {
            return 'Low Demand';
        }

        if (
            $stdDeviation > 5 &&
            $usedPercent <= 0.4 &&
            $averageUsage > 2
        ) {
            return 'Seasonal';
        }

        if (
            $stockNow > ($averageUsage * $OVERSTOCKED_MULTIPLIER) &&
            $averageUsage <= 1
        ) {
            return 'Overstocked';
        }

        return 'Uncategorized';
    }

    /**
     * Classify part demand based on stock-in/out history and current stock.
     *
     * @param array<int> $stockInHistory   Weekly stock-in quantities
     * @param array<int> $stockOutHistory  Weekly stock-out quantities
     * @param int        $stockNow         Current stock on hand
     *
     * @return string                      One of: 'High Demand', 'Stable', 'Low Demand', 'Seasonal', 'Overstock', 'Dormant', 'Uncategorized'
     */
    public static function classifyWithInOut(array $stockInHistory, array $stockOutHistory, int $stockNow): string
    {
        $weeks = count($stockOutHistory);

        if ($weeks < 4 || count($stockInHistory) !== $weeks) {
            return 'Uncategorized';
        }

        $totalIn = array_sum($stockInHistory);
        $totalOut = array_sum($stockOutHistory);
        $averageOut = $totalOut / $weeks;

        $usedWeeks = count(array_filter($stockOutHistory, fn($v) => $v > 0));
        $usedPercent = $usedWeeks / $weeks;

        $inWeeks = count(array_filter($stockInHistory, fn($v) => $v > 0));
        $inPercent = $inWeeks / $weeks;

        // Calculate standard deviation for out transactions
        $variance = array_sum(array_map(
            fn($v) => pow($v - $averageOut, 2),
            $stockOutHistory
        )) / $weeks;
        $stdDeviation = sqrt($variance);

        // Check if activity is only in early weeks
        $outNonZeroIndexes = array_keys(array_filter($stockOutHistory));
        $outOnlyEarly = !empty($outNonZeroIndexes) && max($outNonZeroIndexes) < ($weeks * 0.3);

        // Calculate in/out ratio for balance analysis
        $inOutRatio = $totalOut > 0 ? $totalIn / $totalOut : ($totalIn > 0 ? 999 : 1);

        // === Improved Classification Rules ===

        // 1. Uncategorized: Parts with no stock, no stock-in, and no stock-out transactions
        if ($totalIn == 0 && $totalOut == 0 && $stockNow == 0) {
            return 'Uncategorized';
        }

        // 2. Dormant: Parts with current stock > 0 but no recent transactions (no stock-in or stock-out activity)
        if ($stockNow > 0 && $totalIn == 0 && $totalOut == 0) {
            return 'Dormant';
        }

        // 3. Dormant (Extended): Parts with stock but very minimal activity
        if (
            $stockNow > 0 &&
            (
                ($totalOut <= 2 && $usedPercent <= 0.25) || // Very low outflow activity
                ($outOnlyEarly && $totalOut <= 5) // Activity only in early weeks, now idle
            )
        ) {
            return 'Dormant';
        }

        // 4. Overstock: Stock significantly exceeds recent usage patterns
        if ($averageOut > 0 && $stockNow > ($averageOut * $weeks * 2)) {
            return 'Overstock';
        }

        // 5. High Demand: Consistently high outflow with regular usage
        if (
            $averageOut >= 5 &&
            $usedPercent >= 0.7 &&
            $totalOut > $totalIn &&
            $stdDeviation <= ($averageOut * 0.5) // Consistent usage
        ) {
            return 'High Demand';
        }

        // 6. Stable: Parts with balanced stock-in/stock-out activity (reasonably balanced ratio)
        if (
            $totalOut > 0 &&
            $totalIn > 0 &&
            $inOutRatio >= 0.6 && $inOutRatio <= 1.7 && // Reasonably balanced in/out ratio
            $usedPercent >= 0.4 &&
            $inPercent >= 0.3 &&
            $stdDeviation <= ($averageOut + 2) // Relatively consistent usage
        ) {
            return 'Stable';
        }

        // 7. Low Demand: Regular but minimal usage (prioritize over Stable for low volumes)
        if (
            $averageOut > 0 && $averageOut <= 2.5 &&
            $usedPercent >= 0.4 &&
            $usedPercent <= 0.8 &&
            $stockNow <= ($averageOut * 6) && // Stock not excessive for usage
            $stdDeviation <= 1.5 // Consistent low usage
        ) {
            return 'Low Demand';
        }

        // 8. Seasonal: Irregular usage with high variation and low frequency
        if (
            $usedPercent <= 0.3 &&
            $averageOut >= 2 &&
            $stdDeviation > ($averageOut * 2) && // High variation indicates seasonal pattern
            $totalOut > 0 // Must have some outflow
        ) {
            return 'Seasonal';
        }

        // 9. Handle remaining cases with activity but don't fit specific patterns
        if ($totalOut > 0 || $totalIn > 0) {
            // Has some activity but doesn't fit other categories
            if ($stockNow > 0) {
                // Has stock and some activity - classify based on dominant pattern
                if ($averageOut <= 1) {
                    return 'Low Demand';
                } else {
                    return 'Stable';
                }
            } else {
                // No stock but has activity - likely depleted
                return 'High Demand';
            }
        }

        // 10. Final fallback - only for parts with truly no activity and no stock
        return 'Uncategorized';
    }
}
