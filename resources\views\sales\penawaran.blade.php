@extends('sales.contentsales')
@section('resourcesales')
@vite(['resources/js/sales/penawaran.js'])
<!-- SweetAlert2 CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css">
<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>
<!-- Dropify for file uploads -->
<link href="{{ asset('assets/libs/dropify/dropify.min.css') }}" rel="stylesheet" type="text/css" />
<script src="{{ asset('assets/libs/dropify/dropify.min.js') }}"></script>
<script src="{{ asset('assets/js/pages/form-fileuploads.init.js') }}"></script>
<!-- Filter Script -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Show filter container if any filter is applied
        const dateFrom = document.getElementById('date-from');
        const dateTo = document.getElementById('date-to');
        const customerFilter = document.getElementById('customer-filter');
        const filterContainer = document.getElementById('filter-container');

        // Check if any filter is applied
        if ((dateFrom && dateFrom.value && dateFrom.value !== '{{ now()->startOfMonth()->format('Y-m-d') }}') ||
            (dateTo && dateTo.value && dateTo.value !== '{{ now()->format('Y-m-d') }}') ||
            (customerFilter && customerFilter.value)) {
            // Show filter container
            if (filterContainer) {
                filterContainer.style.display = 'block';
            }
        }

        // Add direct event listener to invoice buttons
        document.querySelectorAll('.btn-invoice-penawaran').forEach(function(btn) {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Invoice button clicked:', this.dataset.id);

                // Try to show modal directly
                const id = this.dataset.id;
                const modal = new bootstrap.Modal(document.getElementById('invoiceModal'));

                // Set penawaran ID
                document.getElementById('penawaran_id').value = id;

                // Show modal
                modal.show();
            });
        });
    });
</script>
<style>
    .w-fit-content {
        width: fit-content;
    }
    .shadow-kit {
        border: 1px ;
        border-radius: 0.5rem;
        background-color: #fff;
    }

    /* Custom part modal styling */
    .currency-input {
        text-align: right;
    }

    #custom-part-modal .modal-body .form-label {
        font-size: 11px;
        font-weight: 600;
        margin-bottom: 4px;
    }

    #custom-part-modal .modal-body .form-control,
    #custom-part-modal .modal-body .form-select {
        font-size: 11px;
        padding: 6px 8px;
    }
</style>
@endsection
@section('contentsales')

@include('sales.partials.navigation')

<div class="content">
    <div class="container-fluid">
        <!-- <div class="row">
            <div class="col-md-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="javascript: void(0);">Sales</a></li>
                            <li class="breadcrumb-item active">Buat Penawaran</li>
                        </ol>
                    </div>
                    <h4 class="page-title">Buat Penawaran</h4>
                </div>
            </div>
        </div> -->

        <div class="row">
            <!-- Left side: Daftar Penawaran -->
            <div id="table-container" class="col-md-7">
                <div class="bgwhite shadow-kit rounded-lg mb-4">
                    <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <h5 class="mb-0 font-bold text-uppercase text-white me-3">Daftar Penawaran</h5>
                        </div>
                        <div class="d-flex align-items-center">
                            <button type="button" id="btn-show-filter" class="btn btn-sm btn-info me-2">
                                <i class="mdi mdi-filter"></i> Filter
                            </button>
                            <button type="button" id="btn-show-form" class="btn btn-sm btn-light">
                                <i class="mdi mdi-plus"></i> Tambah Penawaran
                            </button>
                        </div>
                    </div>
                    <!-- Filter Section (Hidden by default) -->
                    <div id="filter-container" class="card-body border-bottom" style="display: none;">
                        <form id="filter-form" class="row g-3">
                            <div class="col-md-6">
                                <label for="date-from" class="form-label">Tanggal Mulai</label>
                                <input type="date" class="form-control" id="date-from" name="date_from" value="{{ $dateFrom ?? now()->startOfMonth()->format('Y-m-d') }}">
                            </div>
                            <div class="col-md-6">
                                <label for="date-to" class="form-label">Tanggal Akhir</label>
                                <input type="date" class="form-control" id="date-to" name="date_to" value="{{ $dateTo ?? now()->format('Y-m-d') }}">
                            </div>
                            <div class="col-md-6">
                                <label for="customer-filter" class="form-label">Customer</label>
                                <input type="text" class="form-control" id="customer-filter" name="customer" placeholder="Cari berdasarkan nama customer..." value="{{ $customerFilter ?? '' }}">
                            </div>
                            <div class="col-md-6">
                                <label for="month-filter" class="form-label">Bulan</label>
                                <select id="month-filter" name="month" class="form-select">
                                    <option value="">Semua Bulan</option>
                                    @foreach($availableMonths as $availableMonth)
                                        @php
                                            $monthName = \Carbon\Carbon::create()->month($availableMonth->month)->locale('id')->monthName;
                                        @endphp
                                        <option value="{{ $availableMonth->month }}" data-year="{{ $availableMonth->year }}"
                                            {{ ($month == $availableMonth->month && $year == $availableMonth->year) ? 'selected' : '' }}>
                                            {{ $monthName }} {{ $availableMonth->year }}
                                        </option>
                                    @endforeach
                                </select>
                                <input type="hidden" name="year" id="year-filter" value="{{ $year }}">
                            </div>
                            <div class="col-12 d-flex justify-content-end">
                                <button type="button" id="btn-reset-filter" class="btn btn-sm btn-secondary me-2">
                                    <i class="mdi mdi-refresh"></i> Reset
                                </button>
                                <button type="submit" class="btn btn-sm btn-primary">
                                    <i class="mdi mdi-filter"></i> Terapkan Filter
                                </button>
                            </div>
                        </form>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered w-100" id="penawaran-table">
                                <thead class="bg-light" style="font-size: 11px;">
                                    <tr>
                                        <th>No</th>
                                        <th>Nomor Penawaran</th>
                                        <th>Perihal</th>
                                        <th>Customer</th>
                                        <th>Tanggal</th>
                                        <th>Total</th>
                                        <th>Status</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody style="font-size: 11px;">
                                    @foreach($penawarans as $index => $penawaran)
                                    <tr>
                                        <td>{{ $index + 1 }}</td>
                                        <td>{{ $penawaran->nomor }}</td>
                                        <td>{{ $penawaran->perihal }}</td>
                                        <td>{{ $penawaran->customer }}</td>
                                        <td>
                                            @if($penawaran->tanggal_penawaran)
                                                {{ $penawaran->tanggal_penawaran->format('d/m/Y') }}
                                            @else
                                                {{ $penawaran->created_at->format('d/m/Y') }}
                                            @endif
                                        </td>
                                        <td class="text-end text-nowrap">{{ number_format($penawaran->total, 0, ',', '.') }}</td>
                                        <td>
                                            @php
                                                $statusClass = 'bg-primary text-white';
                                                switch($penawaran->status) {
                                                    case 'Draft':
                                                        $statusClass = 'bg-secondary';
                                                        break;
                                                    case 'Dikirim ke customer':
                                                        $statusClass = 'bg-info text-white';
                                                        break;
                                                    case 'Pending PO':
                                                        $statusClass = 'bg-warning text-black';
                                                        break;
                                                    case 'Pending GR':
                                                        $statusClass = 'bg-warning text-black';
                                                        break;
                                                    case 'Proses penyediaan':
                                                        $statusClass = 'bg-warning text-black';
                                                        break;
                                                    case 'Selesai':
                                                        $statusClass = 'bg-success text-white';
                                                        break;
                                                }
                                            @endphp
                                            <span class="badge {{ $statusClass }}">{{ $penawaran->status }}</span>
                                        </td>
                                        <td>
                                            <a href="{{ route('sales.penawaran.pdf', $penawaran->id) }}" class="btn btn-sm btn-primary" target="_blank">
                                                <i class="mdi mdi-file-pdf"></i> Penawaran
                                            </a>
                                            <button class="btn btn-sm btn-success btn-invoice-penawaran" data-id="{{ $penawaran->id }}">
                                                <i class="mdi mdi-printer"></i> Invoice
                                            </button>
                                            <button class="btn btn-sm btn-info btn-status-penawaran" data-id="{{ $penawaran->id }}" data-status="{{ $penawaran->status }}">
                                                <i class="mdi mdi-update"></i> Ubah Status
                                            </button>
                                            <button class="btn btn-sm btn-primary btn-edit-penawaran" data-id="{{ $penawaran->id }}">
                                                <i class="mdi mdi-pencil"></i>
                                            </button>
                                            <button class="btn btn-sm btn-danger btn-delete-penawaran" data-id="{{ $penawaran->id }}">
                                                <i class="mdi mdi-delete"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mt-1">
                            <div>
                                <span>Showing {{ $penawarans->firstItem() ?? 0 }} to {{ $penawarans->lastItem() ?? 0 }} of {{ $penawarans->total() }} entries</span>
                            </div>
                            <div>
                                {{ $penawarans->links() }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right side: Form Penawaran (Hidden initially) -->
            <div class="col-md-5" id="form-container" style="display: none;">
                <div class="bgwhite shadow-kit rounded-lg mb-4 sticky-top" style="top: 10px; z-index: 100;">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center pt-2 pb-1 pl-2 pr-2">
                        <h5 class="mb-0 font-bold text-uppercase text-white">Form Penawaran</h5>
                        <div>
                            <button type="button" id="btn-reset-form" class="btn btn-sm btn-light me-1">
                                <i class="mdi mdi-refresh"></i> Reset
                            </button>
                            <button type="button" id="btn-close-form" class="btn btn-sm btn-light">
                                <i class="mdi mdi-close"></i> Tutup
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <form id="penawaran-form">
                            <div class="mb-3">
                                <label for="nomor" class="form-label">Nomor Penawaran</label>
                                <input type="text" class="form-control" id="nomor" name="nomor" required>
                            </div>
                            <div class="mb-3">
                                <label for="tanggal_penawaran" class="form-label">Tanggal Penawaran</label>
                                <input type="date" class="form-control" id="tanggal_penawaran" name="tanggal_penawaran" value="{{ date('Y-m-d') }}">
                            </div>
                            <div class="mb-3">
                                <label for="perihal" class="form-label">Perihal</label>
                                <input type="text" class="form-control" id="perihal" name="perihal" required>
                            </div>
                            <div class="mb-3 position-relative">
                                <label for="customer" class="form-label">Customer</label>
                                <input type="text" class="form-control" id="customer" name="customer" required autocomplete="off">
                                <div id="customer-suggestions" class="list-group position-absolute w-100" style="display: none; max-height: 200px; overflow-y: auto; z-index: 1000;"></div>
                            </div>
                            <div class="mb-3">
                                <label for="attn" class="form-label">Attn (Attention)</label>
                                <input type="text" class="form-control" id="attn" name="attn">
                                <small class="text-muted">Nama kontak person</small>
                            </div>
                            <div class="mb-3">
                                <label for="lokasi" class="form-label">Lokasi</label>
                                <input type="text" class="form-control" id="lokasi" name="lokasi" required>
                            </div>
                            <div class="mb-3">
                                <label for="notes" class="form-label">Notes</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                                <small class="text-muted">Notes akan ditampilkan dalam format list</small>
                            </div>
                            <div class="mb-3">
                                <label for="diskon" class="form-label">Diskon</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="diskon" name="diskon" min="0" max="100" step="0.5" value="0">
                                    <span class="input-group-text">%</span>
                                </div>
                                <small class="text-muted">Masukkan nilai diskon dalam persen (contoh: 5 untuk diskon 5%, 10 untuk diskon 10%)</small>
                            </div>
                            <div class="mb-3" id="status-container" style="display: none;">
                                <label for="status" class="form-label">Status</label>
                                <select class="btn btn-secondary btn-sm" id="status" name="status">
                                    <option value="Draft">Draft</option>
                                    <option value="Dikirim ke customer">Dikirim ke customer</option>
                                    <option value="PO customer">PO customer</option>
                                    <option value="Pending PO">Pending PO</option>
                                    <option value="pending GR">pending GR</option>
                                    <option value="Proses penyediaan">Proses penyediaan</option>
                                    <option value="Selesai">Selesai</option>
                                </select>
                            </div>
                            <div class="mb-3 position-relative">
                                <label class="form-label">Tambah Part</label>
                                <input type="text" class="form-control" id="part-search" placeholder="Cari part...">
                                <div id="part-suggestions" class="list-group position-absolute w-100" style="display: none; max-height: 200px; overflow-y: auto; z-index: 1000;"></div>
                                <div class="mt-2">
                                    <button type="button" id="btn-add-custom-part" class="btn btn-sm btn-outline-primary">
                                        <i class="mdi mdi-plus"></i> Tambah Part Baru
                                    </button>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-bordered table-sm" id="parts-table">
                                    <thead class="bg-light">
                                        <tr>
                                            <th>Part Code</th>
                                            <th>Qty</th>
                                            <th>Price</th>
                                            <th>Total</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody id="parts-table-body">
                                        <!-- Parts will be added here dynamically -->
                                    </tbody>
                                    <tfoot>
                                        <tr>
                                            <th colspan="3" class="text-end">Grand Total</th>
                                            <th id="grand-total" class="text-nowrap">Rp 0</th>
                                            <th></th>
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>

                            <div class="d-grid gap-2 mt-3">
                                <button type="submit" class="btn btn-primary">
                                    <i class="mdi mdi-content-save"></i> Simpan Penawaran
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- View Penawaran Modal -->
<div class="modal fade" id="view-penawaran-modal" tabindex="-1" role="dialog" aria-labelledby="view-penawaran-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title text-white" id="view-penawaran-modal-label">Detail Penawaran</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <table class="table table-sm table-bordered">
                            <tr>
                                <th class="bg-light">Nomor Penawaran</th>
                                <td id="view-nomor"></td>
                            </tr>
                            <tr>
                                <th class="bg-light">Perihal</th>
                                <td id="view-perihal"></td>
                            </tr>
                            <tr>
                                <th class="bg-light">Customer</th>
                                <td id="view-customer"></td>
                            </tr>
                            <tr>
                                <th class="bg-light">Attn</th>
                                <td id="view-attn"></td>
                            </tr>
                            <tr>
                                <th class="bg-light">Lokasi</th>
                                <td id="view-lokasi"></td>
                            </tr>
                            <tr>
                                <th class="bg-light">Tanggal Penawaran</th>
                                <td id="view-tanggal"></td>
                            </tr>
                            <tr>
                                <th class="bg-light">Status</th>
                                <td id="view-status"></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">Notes</h6>
                            </div>
                            <div class="card-body">
                                <div id="view-notes"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-bordered" id="view-parts-table">
                        <thead class="bg-light">
                            <tr>
                                <th>Part</th>
                                <th>Quantity</th>
                                <th>Price</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody id="view-parts-table-body">
                            <!-- Parts will be added here dynamically -->
                        </tbody>
                        <tfoot>
                            <tr>
                                <th colspan="3" class="text-end">Grand Total</th>
                                <th id="view-grand-total" class="text-nowrap">Rp 0</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                <a href="#" id="view-pdf-link" class="btn btn-primary" target="_blank">
                    <i class="mdi mdi-file-pdf"></i> View File
                </a>
                <a href="#" id="view-invoice-link" class="btn btn-success" target="_blank">
                    <i class="mdi mdi-printer"></i> Invoice
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Hidden input for editing -->
<input type="hidden" id="edit-penawaran-id">

<!-- Custom Part Modal -->
<div class="modal fade" id="custom-part-modal" tabindex="-1" role="dialog" aria-labelledby="custom-part-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title text-white" id="custom-part-modal-label">Tambah Part Baru</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" style="font-size: 11px;">
                <form id="custom-part-form">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="custom-part-code" class="form-label">Kode Part <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="custom-part-code" name="part_code" placeholder="Kode Part" required style="font-size: 11px; padding: 6px 8px;">
                            </div>
                            <div class="mb-3">
                                <label for="custom-part-name" class="form-label">Nama Part <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="custom-part-name" name="part_name" placeholder="Nama Part" required style="font-size: 11px; padding: 6px 8px;">
                            </div>
                            <div class="mb-3">
                                <label for="custom-part-type" class="form-label">Tipe Part <span class="text-danger">*</span></label>
                                <select class="form-control" id="custom-part-type" name="part_type" required style="font-size: 11px; padding: 6px 8px;">
                                    <option value="">Pilih Tipe Part</option>
                                    <option value="AC">AC</option>
                                    <option value="TYRE">TYRE</option>
                                    <option value="FABRIKASI">FABRIKASI</option>
                                    <option value="PERLENGKAPAN AC">PERLENGKAPAN AC</option>
                                    <option value="PERSEDIAAN LAINNYA">PERSEDIAAN LAINNYA</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="custom-part-quantity" class="form-label">Jumlah <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="custom-part-quantity" name="quantity" value="1" min="1" required style="font-size: 11px; padding: 6px 8px;">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="custom-part-price" class="form-label">Harga Jual <span class="text-danger">*</span></label>
                                <input type="text" class="form-control currency-input" id="custom-part-price" name="price" placeholder="Rp 0" required style="font-size: 11px; padding: 6px 8px; text-align: right;">
                            </div>
                            <div class="mb-3">
                                <label for="custom-part-purchase-price" class="form-label">Harga Beli</label>
                                <input type="text" class="form-control currency-input" id="custom-part-purchase-price" name="purchase_price" placeholder="Rp 0" style="font-size: 11px; padding: 6px 8px; text-align: right;">
                            </div>
                            <div class="mb-3">
                                <label for="custom-part-eum" class="form-label">EUM</label>
                                <input type="text" class="form-control" id="custom-part-eum" name="eum" placeholder="EA" value="EA" maxlength="5" style="font-size: 11px; padding: 6px 8px;">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" style="font-size: 11px; padding: 4px 12px;">Batal</button>
                <button type="button" class="btn btn-primary" id="btn-save-custom-part" style="font-size: 11px; padding: 4px 12px;">Tambahkan</button>
            </div>
        </div>
    </div>
</div>

<!-- Invoice Modal -->
<div class="modal fade" id="invoiceModal" tabindex="-1" aria-labelledby="invoiceModalLabel" aria-hidden="true">
    <div style="min-width: 70vw; font-size: 11px;" class="modal-dialog">
        <div class="modal-content p-4">
            <div class="modal-header">
                <h5 class="modal-title font-bold text-uppercase" id="invoiceModalLabel">Invoice Penawaran</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="invoiceForm">
                    <input type="hidden" id="penawaran_id" name="penawaran_id">
                    <input type="hidden" id="invoice_id" name="invoice_id">
                    <div class="row mb-3">
                        <label for="model_unit" class="col-sm-2 col-form-label">Model/Unit</label>
                        <div class="col-sm-4 mb-1">
                            <input type="text" class="form-control" id="model_unit" name="model_unit" required>
                        </div>
                        <label for="transfer_to" class="col-sm-2 col-form-label">Ditransfer ke</label>
                        <div class="col-sm-4 mb-1">
                            <input type="text" class="form-control" id="transfer_to" name="transfer_to" value="PT.PUTERA WIBOWO BORNEO" required>
                        </div>
                        <label for="hmkm" class="col-sm-2 col-form-label">HM/KM</label>
                        <div class="col-sm-4 mb-1">
                            <input type="text" class="form-control" id="hmkm" name="hmkm" required>
                        </div>
                        <label for="bank_account" class="col-sm-2 col-form-label">No Rekening</label>
                        <div class="col-sm-4 mb-1">
                            <input type="text" class="form-control" id="bank_account" name="bank_account" value="0623-01-001201-30-0" required>
                        </div>
                        <label for="sn" class="col-sm-2 col-form-label">S/N</label>
                        <div class="col-sm-4 mb-1">
                            <input type="text" class="form-control" id="sn" name="sn" required>
                        </div>
                        <label for="bank_branch" class="col-sm-2 col-form-label">Cabang Rekening</label>
                        <div class="col-sm-4 mb-1">
                            <input type="text" class="form-control" id="bank_branch" name="bank_branch" value="Bank BRI, Cabang Banjarmasin A Yani" required>
                        </div>
                        <label for="trouble" class="col-sm-2 col-form-label">Trouble</label>
                        <div class="col-sm-4 mb-1">
                            <input type="text" class="form-control" id="trouble" name="trouble" required>
                        </div>
                        <label for="invoice_lokasi" class="col-sm-2 col-form-label">Lokasi</label>
                        <div class="col-sm-4 mb-1">
                            <input type="text" class="form-control" id="invoice_lokasi" name="lokasi" required>
                        </div>
                        <label for="tanggal_invoice" class="col-sm-2 col-form-label">Tanggal Invoice</label>
                        <div class="col-sm-4 mb-1">
                            <input type="date" class="form-control" id="tanggal_invoice" name="tanggal_invoice" value="{{ date('Y-m-d') }}">
                        </div>
                        <label for="due_date" class="col-sm-2 col-form-label">Tanggal Jatuh Tempo</label>
                        <div class="col-sm-4 mb-1">
                            <input type="date" class="form-control" id="due_date" name="due_date">
                        </div>
                        <label for="no_invoice" class="col-sm-2 col-form-label">Nomor Invoice</label>
                        <div class="col-sm-4 mb-1">
                            <input type="text" class="form-control" id="no_invoice" name="no_invoice" placeholder="Nomor Invoice">
                        </div>
                        <label for="po_number" class="col-sm-2 col-form-label">Nomor PO</label>
                        <div class="col-sm-4">
                            <input type="text" class="form-control" id="po_number" name="po_number" placeholder="Nomor PO Customer">
                        </div>
                        <label for="notes" class="col-sm-2 col-form-label">Catatan</label>
                        <div class="col-sm-4">
                            <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <!-- <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button> -->
                <button type="button" class="btn btn-primary" id="saveInvoice">Simpan</button>
                <a href="#" id="printInvoice" class="btn btn-success" target="_blank">
                    <i class="mdi mdi-printer"></i> Cetak Invoice
                </a>
            </div>
        </div>
    </div>
</div>
<!-- End of Invoice Modal -->

<!-- Script for automatic due date calculation in invoice modal -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Set initial due date (60 days from today)
        const today = new Date();
        const initialDueDate = new Date(today);
        initialDueDate.setDate(initialDueDate.getDate() + 60);

        // Format the date as YYYY-MM-DD for the input
        const formattedInitialDueDate = initialDueDate.toISOString().split('T')[0];

        // Set the initial due date value
        const dueDateField = document.getElementById('due_date');
        if (dueDateField) {
            dueDateField.value = formattedInitialDueDate;
        }

        // Add event listener for invoice date change
        const invoiceDateField = document.getElementById('tanggal_invoice');
        if (invoiceDateField) {
            invoiceDateField.addEventListener('change', function() {
                const invoiceDate = new Date(this.value);
                if (!isNaN(invoiceDate.getTime()) && dueDateField) {
                    // Calculate due date (60 days from invoice date)
                    const dueDate = new Date(invoiceDate);
                    dueDate.setDate(dueDate.getDate() + 60);

                    // Format the date as YYYY-MM-DD for the input
                    const formattedDueDate = dueDate.toISOString().split('T')[0];
                    dueDateField.value = formattedDueDate;
                }
            });
        }
    });
</script>

<!-- Status Change Modal -->
<div class="modal fade" id="statusModal" tabindex="-1" aria-labelledby="statusModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title font-bold text-uppercase" id="statusModalLabel">Ubah Status Penawaran</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="statusForm" enctype="multipart/form-data">
                    <input type="hidden" id="status_penawaran_id" name="penawaran_id">
                    <input type="hidden" id="current_status" name="current_status">

                    <div class="mb-3">
                        <label for="new_status" class="form-label">Status Baru</label>
                        <select class="form-select" id="new_status" name="new_status">
                            <option value="Draft">Draft</option>
                            <option value="Dikirim ke customer">Dikirim ke customer</option>
                            <option value="PO customer">PO customer</option>
                            <option value="Pending PO">Pending PO</option>
                            <option value="pending GR">pending GR</option>
                            <option value="Proses penyediaan">Proses penyediaan</option>
                            <option value="Selesai">Selesai</option>
                        </select>
                    </div>

                    <div id="invoice_status_section" class="mb-3" style="display: none;">
                        <div class="alert alert-info">
                            <p>Status penawaran akan diubah menjadi <strong>Selesai</strong>.</p>
                            <p>Invoice terkait juga akan diperbarui dengan status <strong>Selesai</strong>.</p>
                            <p>Silakan lampirkan dokumen pendukung (invoice yang telah ditandatangani, dll).</p>
                        </div>

                        <div class="mb-3">
                            <label for="invoice_documents" class="form-label">Dokumen Pendukung</label>
                            <input type="file" class="form-control dropify" id="invoice_documents" name="invoice_documents[]" accept=".pdf,.jpg,.jpeg,.png,.doc,.docx">
                            <small class="text-muted">Unggah dokumen pendukung (PDF, JPG, PNG, DOC, DOCX). Maksimal 10MB. Dokumen baru akan menggantikan dokumen lama.</small>
                        </div>

                        <div class="mb-3">
                            <label for="invoice_notes" class="form-label">Catatan</label>
                            <textarea class="form-control" id="invoice_notes" name="invoice_notes" rows="3"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-primary" id="saveStatus">Simpan Perubahan</button>
            </div>
        </div>
    </div>
</div>
<!-- End of Status Change Modal -->

@endsection