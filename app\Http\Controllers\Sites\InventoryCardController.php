<?php

namespace App\Http\Controllers\Sites;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Site;
use App\Models\Part;
use Illuminate\Support\Facades\DB;

class InventoryCardController extends Controller
{
    public function __construct()
    {
        // No middleware check, we'll handle it in each method
    }

    public function index(Request $request)
    {
        if (!session('site_id') || !session('role')) {
            return redirect('/login')->withErrors(['username' => 'Silakan login terlebih dahulu']);
        }

        $siteId = session('site_id');
        $site = Site::findOrFail($siteId);

        // Subquery untuk kebutuhan part dalam 13 hari ke depan
        $subQuery = DB::table('daily_reports as dly')
            ->join('units as u', 'u.id', '=', 'dly.unit_id')
            ->join(
                DB::raw('(SELECT unit_id, MAX(date_in) as latest_date FROM daily_reports GROUP BY unit_id) as latest'),
                function ($join) {
                    $join->on('latest.unit_id', '=', 'dly.unit_id')
                        ->on('latest.latest_date', '=', 'dly.date_in');
                }
            )
            ->leftJoin('unit_parts as upt', 'upt.unit_id', '=', 'u.id')
            ->where('u.site_id', $siteId)
            ->whereRaw("DATE_ADD(dly.date_in, INTERVAL ROUND((CEIL(dly.hm / 250) * 250 - dly.hm) / 20) DAY)<= CURDATE() + INTERVAL 13 DAY")
            ->select(
                'upt.part_inventory_id as partinvneed',
                DB::raw('SUM(upt.quantity) as needed')
            )
            ->groupBy('upt.part_inventory_id');

        // Main query
        $parts = DB::table('part_inventories as pi')
            ->join('parts as p', 'p.part_code', '=', 'pi.part_code')
            ->leftJoinSub($subQuery, 'tabel2', function ($join) {
                $join->on('tabel2.partinvneed', '=', 'pi.part_inventory_id');
            })
            ->where('pi.site_id', $siteId)
            ->select(
                'pi.site_id',
                'p.part_name',
                'pi.stock_quantity',
                'pi.min_stock',
                'pi.price',
                'pi.max_stock',
                'tabel2.needed'
            )
            ->orderBy('p.part_name')
            ->paginate(15);

        return view('sites.inventorycard', compact('site', 'parts'));
    }

    public function getInventoryData(Request $request)
    {
        if (!session('site_id') || !session('role')) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $siteId = session('site_id');
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 15);
        $searchTerm = $request->input('search', '');
        $partType = $request->input('part_type', '');
        $statusFilter = $request->input('status_filter', '');
        $sortBy = $request->input('sort_by', 'part_code');
        $sortDirection = $request->input('sort_direction', 'asc');

        // Subquery untuk kebutuhan part berdasarkan HM dan 10 hari
        $subquery = DB::table('daily_reports as dly')
            ->join('units as u', 'u.id', '=', 'dly.unit_id')
            ->join(DB::raw('(
            SELECT unit_id, MAX(date_in) as latest_date
            FROM daily_reports
            GROUP BY unit_id
        ) as latest'), function ($join) {
                $join->on('latest.unit_id', '=', 'dly.unit_id')
                    ->on('latest.latest_date', '=', 'dly.date_in');
            })
            ->leftJoin('unit_parts as upt', 'upt.unit_id', '=', 'u.id')
            ->select('upt.part_inventory_id as partinvneed', DB::raw('SUM(upt.quantity) as needed'))
            ->where('u.site_id', $siteId)
            ->whereRaw("DATE_ADD(dly.date_in, INTERVAL ROUND((CEIL(dly.hm / 250) * 250 - dly.hm) / 20) DAY) <= CURDATE() + INTERVAL 10 DAY")
            ->groupBy('upt.part_inventory_id');

        // Query utama
        $query = DB::table('part_inventories as pi')
            ->join('parts as p', 'p.part_code', '=', 'pi.part_code')
            ->leftJoinSub($subquery, 'tabel2', function ($join) {
                $join->on('tabel2.partinvneed', '=', 'pi.part_inventory_id');
            })
            ->where('pi.site_id', $siteId);

        if (!empty($searchTerm)) {
            $query->where(function ($q) use ($searchTerm) {
                $q->where('p.part_code', 'LIKE', "%{$searchTerm}%")
                    ->orWhere('p.part_name', 'LIKE', "%{$searchTerm}%")
                    ->orWhere('pi.site_part_name', 'LIKE', "%{$searchTerm}%");
            });
        }

        if (!empty($partType)) {
            $query->where('p.part_type', $partType);
        }

        if (!empty($statusFilter) && $statusFilter === 'Lainnya') {
            $query->where('pi.min_stock', 0)->where('pi.max_stock', 0);
        }

        if ($sortBy === 'part_code' || $sortBy === 'part_name') {
            $query->orderBy('p.' . $sortBy, $sortDirection);
        } elseif ($sortBy === 'status') {
            $query->orderBy('p.part_code', 'asc'); 
        } else {
            $query->orderBy($sortBy, $sortDirection);
        }

        $total = $query->count();

        $inventories = $query
            ->select('pi.*', 'p.part_name', 'tabel2.needed')
            ->skip(($page - 1) * $perPage)
            ->take($perPage)
            ->get();

        $data = [];
        foreach ($inventories as $inventory) {
            $minStock = $inventory->min_stock ?? 0;
            $maxStock = $inventory->max_stock ?? 0;
            $currentStock = $inventory->stock_quantity ?? 0;
            $averageStock = ($minStock + $maxStock) / 2;

            $status = 'Lainnya';

            if ($minStock == 0 && $maxStock == 0) {
                $status = 'Lainnya';
            } else {
                if ($currentStock < $averageStock && ($minStock != 0 || $maxStock != 0)) {
                    $status = 'Not Ready';
                } elseif ($currentStock >= $averageStock && $currentStock <= $maxStock) {
                    $status = 'Medium';
                } elseif ($currentStock > $maxStock) {
                    $status = 'Ready';
                }
            }

            $itemData = [
                'part_code' => $inventory->part_code,
                'part_name' => $inventory->site_part_name ?? $inventory->part_name,
                'stock_quantity' => $inventory->stock_quantity,
                'min_stock' => $inventory->min_stock,
                'max_stock' => $inventory->max_stock,
                'price' => $inventory->price ?? 0,
                'status' => $status,
                'needed' => $inventory->needed ?? 0,
            ];

            if ($siteId === 'IMK') {
                $itemData['itemcode'] = $inventory->item_code ?? '-';
            }

            $data[] = $itemData;
        }

        if ($sortBy === 'status') {
            $statusOrder = ['Not Ready' => 1, 'Medium' => 2, 'Ready' => 3, 'Lainnya' => 4];
            usort($data, function ($a, $b) use ($statusOrder, $sortDirection) {
                $aOrder = $statusOrder[$a['status']] ?? 5;
                $bOrder = $statusOrder[$b['status']] ?? 5;
                return $sortDirection === 'desc' ? $bOrder <=> $aOrder : $aOrder <=> $bOrder;
            });
        }

        if ($sortBy === 'needed') {
            $query->orderBy('needed' . $sortBy, $sortDirection);
        }

        return response()->json([
            'data' => array_values($data),
            'total' => $total,
            'current_page' => (int)$page,
            'per_page' => (int)$perPage
        ]);
    }


    public function getPartTypes()
    {
        // Check if user is logged in and has a site_id
        if (!session('site_id') || !session('role')) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $partTypes = Part::PART_TYPES;
        return response()->json(['part_types' => $partTypes]);
    }
}
